/**
 * Vue Composable for Route Preloader
 * 
 * Provides easy access to route preloading functionality and statistics
 */

import { routePreloader, type PreloadConfig, type PreloadResult } from '../utils/routePreloader'

export function useRoutePreloader() {
  /**
   * Check if a route is preloaded
   */
  const isRoutePreloaded = (routeName: string): boolean => {
    return routePreloader.isRoutePreloaded(routeName)
  }

  /**
   * Get preloading statistics
   */
  const getPreloadStats = () => {
    return routePreloader.getStats()
  }

  /**
   * Print preloading statistics to console
   */
  const printPreloadStats = () => {
    const stats = getPreloadStats()
    
    console.group('🚀 Route Preloader Statistics')
    console.log(`Total Preloaded: ${stats.totalPreloaded}`)
    console.log(`Average Load Time: ${stats.averageLoadTime.toFixed(2)}ms`)
    console.log('Preloaded Routes:', stats.preloadedRoutes)
    
    console.group('📊 Detailed Results')
    stats.results.forEach(result => {
      if (result.success) {
        console.log(`✅ ${result.route}: ${result.loadTime.toFixed(2)}ms`)
      } else {
        console.log(`❌ ${result.route}: ${result.error}`)
      }
    })
    console.groupEnd()
    
    console.groupEnd()
  }

  /**
   * Manually preload specific routes
   */
  const preloadRoutes = async (routeNames: string[]) => {
    await routePreloader.preloadSpecificRoutes(routeNames)
  }

  /**
   * Enable/disable preloading
   */
  const setPreloadingEnabled = (enabled: boolean) => {
    routePreloader.setEnabled(enabled)
  }

  /**
   * Update preloader configuration
   */
  const updatePreloadConfig = (config: Partial<PreloadConfig>) => {
    routePreloader.updateConfig(config)
  }

  /**
   * Clear preloaded routes (for testing)
   */
  const clearPreloaded = () => {
    routePreloader.clearPreloaded()
  }

  return {
    // Status functions
    isRoutePreloaded,
    getPreloadStats,
    printPreloadStats,
    
    // Control functions
    preloadRoutes,
    setPreloadingEnabled,
    updatePreloadConfig,
    clearPreloaded
  }
}

/**
 * Setup global preloader utilities
 * Available in browser console as window.preloader
 */
export function setupGlobalPreloaderUtils() {
  if (typeof window !== 'undefined') {
    const { 
      getPreloadStats, 
      printPreloadStats, 
      preloadRoutes, 
      setPreloadingEnabled, 
      clearPreloaded,
      isRoutePreloaded
    } = useRoutePreloader()
    
    ;(window as any).preloader = {
      stats: printPreloadStats,
      enable: () => setPreloadingEnabled(true),
      disable: () => setPreloadingEnabled(false),
      preload: preloadRoutes,
      clear: clearPreloaded,
      isPreloaded: isRoutePreloaded,
      raw: getPreloadStats
    }
    
    console.log('🔧 Route preloader utilities available at window.preloader')
    console.log('Commands: preloader.stats(), preloader.enable(), preloader.disable(), preloader.preload([\'Notes\'])')
  }
}
